# YNAB Off-Target Assignment Analysis - Environment Variables Template
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# OAuth 2.0 Implicit Grant Flow Configuration (RECOMMENDED)
# =============================================================================

# YNAB OAuth Client ID (required for Implicit Grant Flow)
# Get this from https://app.ynab.com/settings/developer
# This is safe to expose in the browser (public client)
NEXT_PUBLIC_YNAB_CLIENT_ID=your_ynab_oauth_client_id_here

# =============================================================================
# NextAuth.js Configuration
# =============================================================================

# NextAuth Secret (required for JWT signing)
# Generate with: openssl rand -base64 32
NEXTAUTH_SECRET=your_nextauth_secret_here

# NextAuth URL (automatically detected in most cases)
# Only needed if you have custom domain setup
# NEXTAUTH_URL=https://your-domain.com

# =============================================================================
# Application Configuration
# =============================================================================

# Environment
NODE_ENV=development

# Application Name
NEXT_PUBLIC_APP_NAME=YNAB Off-Target Analysis

# Application URL (for OAuth redirects)
# In development: http://localhost:3000
# In production: https://your-domain.com
NEXT_PUBLIC_APP_URL=http://localhost:3000

# YNAB API Base URL
NEXT_PUBLIC_API_BASE_URL=https://api.ynab.com/v1

# =============================================================================
# Legacy Configuration (DEPRECATED - Remove after OAuth migration)
# =============================================================================

# Personal Access Token (DEPRECATED - use OAuth instead)
# This should be removed once OAuth migration is complete
# YNAB_ACCESS_TOKEN=your-ynab-personal-access-token-here

# =============================================================================
# Rate Limiting Configuration
# =============================================================================

RATE_LIMIT_REQUESTS_PER_HOUR=200
CACHE_TTL_SECONDS=300

# =============================================================================
# Security Configuration
# =============================================================================

ENABLE_SECURITY_HEADERS=true
LOG_LEVEL=info

# =============================================================================
# Development Configuration
# =============================================================================

NEXT_PUBLIC_ENABLE_DEBUG=false

# Disable telemetry
NEXT_TELEMETRY_DISABLED=1

# =============================================================================
# Setup Instructions
# =============================================================================

# 1. Copy this file to .env.local:
#    cp .env.example .env.local

# 2. Register your application with YNAB:
#    - Go to https://app.ynab.com/settings/developer
#    - Create a new OAuth application
#    - Set redirect URI to: http://localhost:3000/auth/callback (development)
#    - Copy the Client ID to NEXT_PUBLIC_YNAB_CLIENT_ID

# 3. Generate NextAuth secret:
#    openssl rand -base64 32
#    Copy the result to NEXTAUTH_SECRET

# 4. Update NEXT_PUBLIC_APP_URL for your environment

# 5. Start the development server:
#    npm run dev
