# YNAB API Configuration
# Get your Personal Access Token from: https://app.ynab.com/settings/developer
YNAB_ACCESS_TOKEN=your-ynab-personal-access-token-here

# Application Configuration
NODE_ENV=development
NEXT_PUBLIC_APP_NAME=YNAB Off-Target Analysis
NEXT_PUBLIC_API_BASE_URL=https://api.ynab.com/v1

# Rate Limiting Configuration
RATE_LIMIT_REQUESTS_PER_HOUR=200
CACHE_TTL_SECONDS=300

# Security Configuration
ENABLE_SECURITY_HEADERS=true
LOG_LEVEL=info

# Development Configuration
NEXT_PUBLIC_ENABLE_DEBUG=false
