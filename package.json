{"name": "ynab-off-target-assignment", "version": "0.1.0", "private": true, "description": "YNAB Off-Target Assignment Analysis - Budget target alignment dashboard", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/lodash": "^4.17.0", "@types/node": "^20.12.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.0", "axios": "^1.7.0", "date-fns": "^3.6.0", "lodash": "^4.17.21", "next": "^14.2.0", "postcss": "^8.4.0", "react": "^18.3.0", "react-dom": "^18.3.0", "recharts": "^2.12.0", "swr": "^2.2.0", "tailwindcss": "^3.4.0", "typescript": "^5.4.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^7.8.0", "@typescript-eslint/parser": "^7.8.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.0", "prettier": "^3.2.0", "prettier-plugin-tailwindcss": "^0.5.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}