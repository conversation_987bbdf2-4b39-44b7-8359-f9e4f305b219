import { NextRequest, NextResponse } from 'next/server';
import { YNABService } from '@/lib/ynab-service';
import { generateDashboardSummary } from '@/lib/monthly-analysis';
import { getFirstDayOfMonth, validateMonthFormat } from '@/lib/data-processing';
import { SecureErrorHandler } from '@/lib/errors';

/**
 * Validates that a month is within the budget's valid date range
 */
function validateMonthInBudgetRange(month: string, budget: any): { isValid: boolean; error?: string } {
  if (!validateMonthFormat(month)) {
    return { isValid: false, error: `Invalid month format: ${month}. Expected YYYY-MM-DD format.` };
  }

  const requestedDate = new Date(month);
  const budgetFirstDate = new Date(budget.firstMonth);
  const budgetLastDate = new Date(budget.lastMonth);

  if (requestedDate < budgetFirstDate) {
    return {
      isValid: false,
      error: `Month ${month} is before budget start date ${budget.firstMonth}`
    };
  }

  if (requestedDate > budgetLastDate) {
    return {
      isValid: false,
      error: `Month ${month} is after budget end date ${budget.lastMonth}`
    };
  }

  return { isValid: true };
}

/**
 * Gets a safe default month within the budget's range
 */
function getSafeDefaultMonth(budget: any): string {
  const currentMonth = getFirstDayOfMonth();
  const budgetLastDate = new Date(budget.lastMonth);
  const budgetFirstDate = new Date(budget.firstMonth);
  const currentDate = new Date(currentMonth);

  // If current month is within budget range, use it
  if (currentDate >= budgetFirstDate && currentDate <= budgetLastDate) {
    return currentMonth;
  }

  // If current month is after budget range, use budget's last month
  if (currentDate > budgetLastDate) {
    return budget.lastMonth;
  }

  // If current month is before budget range, use budget's first month
  return budget.firstMonth;
}

/**
 * Structured logging for API errors
 */
function logAPIError(context: string, error: any, requestInfo: any) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    context,
    error: {
      name: error.name,
      message: error.message,
      type: error.type || 'unknown',
      statusCode: error.statusCode || 500,
    },
    request: requestInfo,
  };

  console.error(`[${context}] API Error:`, JSON.stringify(logEntry, null, 2));
}

/**
 * GET /api/analysis/monthly
 * Analyze monthly budget target alignment
 *
 * Query parameters:
 * - budgetId: YNAB budget ID (optional, uses default if not provided)
 * - month: Month in YYYY-MM-DD format (optional, uses safe default if not provided)
 */
export async function GET(request: NextRequest) {
  const requestInfo = {
    method: 'GET',
    url: request.url,
    budgetId: null as string | null,
    month: null as string | null,
  };

  try {
    const { searchParams } = new URL(request.url);
    const budgetId = searchParams.get('budgetId');
    const month = searchParams.get('month');

    requestInfo.budgetId = budgetId;
    requestInfo.month = month;

    // Get budget (use default if not specified)
    const budget = budgetId
      ? await YNABService.getBudget(budgetId)
      : await YNABService.getDefaultBudget();

    // Determine analysis month with validation
    let analysisMonth: string;

    if (month) {
      // Validate provided month is within budget range
      const validation = validateMonthInBudgetRange(month, budget);
      if (!validation.isValid) {
        logAPIError('MONTH_VALIDATION', new Error(validation.error!), requestInfo);
        return NextResponse.json({
          success: false,
          error: {
            type: 'invalid_month',
            message: validation.error,
            statusCode: 400,
            availableRange: {
              firstMonth: budget.firstMonth,
              lastMonth: budget.lastMonth,
            }
          }
        }, { status: 400 });
      }
      analysisMonth = month;
    } else {
      // Use safe default month within budget range
      analysisMonth = getSafeDefaultMonth(budget);
    }

    // Log successful request info
    console.log(`[MONTHLY_ANALYSIS] Processing request: Budget=${budget.name} (${budget.id}), Month=${analysisMonth}`);

    // Fetch month data
    const monthData = await YNABService.getMonth(budget.id, analysisMonth);

    // Generate comprehensive analysis
    const dashboardSummary = generateDashboardSummary(
      monthData,
      budget.id,
      budget.name
    );

    return NextResponse.json({
      success: true,
      data: dashboardSummary,
      metadata: {
        budgetId: budget.id,
        budgetName: budget.name,
        month: analysisMonth,
        budgetRange: {
          firstMonth: budget.firstMonth,
          lastMonth: budget.lastMonth,
        },
        generatedAt: new Date().toISOString(),
        cacheStats: YNABService.getCacheStats(),
      }
    });

  } catch (error) {
    logAPIError('MONTHLY_ANALYSIS', error, requestInfo);

    const appError = SecureErrorHandler.handleAPIError(error, 'MONTHLY_ANALYSIS');

    return NextResponse.json({
      success: false,
      error: {
        type: appError.type,
        message: appError.userMessage,
        statusCode: appError.statusCode,
      }
    }, { status: appError.statusCode });
  }
}

/**
 * POST /api/analysis/monthly
 * Analyze monthly data with custom configuration
 */
export async function POST(request: NextRequest) {
  const requestInfo = {
    method: 'POST',
    url: request.url,
    budgetId: null as string | null,
    month: null as string | null,
  };

  try {
    const body = await request.json();
    const { budgetId, month, config } = body;

    requestInfo.budgetId = budgetId;
    requestInfo.month = month;

    // Get budget
    const budget = budgetId
      ? await YNABService.getBudget(budgetId)
      : await YNABService.getDefaultBudget();

    // Determine analysis month with validation
    let analysisMonth: string;

    if (month) {
      // Validate provided month is within budget range
      const validation = validateMonthInBudgetRange(month, budget);
      if (!validation.isValid) {
        logAPIError('MONTH_VALIDATION_POST', new Error(validation.error!), requestInfo);
        return NextResponse.json({
          success: false,
          error: {
            type: 'invalid_month',
            message: validation.error,
            statusCode: 400,
            availableRange: {
              firstMonth: budget.firstMonth,
              lastMonth: budget.lastMonth,
            }
          }
        }, { status: 400 });
      }
      analysisMonth = month;
    } else {
      // Use safe default month within budget range
      analysisMonth = getSafeDefaultMonth(budget);
    }

    // Log successful request info
    console.log(`[MONTHLY_ANALYSIS_POST] Processing request: Budget=${budget.name} (${budget.id}), Month=${analysisMonth}`);

    // Fetch month data
    const monthData = await YNABService.getMonth(budget.id, analysisMonth);

    // Generate analysis with custom config
    const dashboardSummary = generateDashboardSummary(
      monthData,
      budget.id,
      budget.name,
      config
    );

    return NextResponse.json({
      success: true,
      data: dashboardSummary,
      metadata: {
        budgetId: budget.id,
        budgetName: budget.name,
        month: analysisMonth,
        budgetRange: {
          firstMonth: budget.firstMonth,
          lastMonth: budget.lastMonth,
        },
        config,
        generatedAt: new Date().toISOString(),
      }
    });

  } catch (error) {
    logAPIError('MONTHLY_ANALYSIS_POST', error, requestInfo);

    const appError = SecureErrorHandler.handleAPIError(error, 'MONTHLY_ANALYSIS_POST');

    return NextResponse.json({
      success: false,
      error: {
        type: appError.type,
        message: appError.userMessage,
        statusCode: appError.statusCode,
      }
    }, { status: appError.statusCode });
  }
}
