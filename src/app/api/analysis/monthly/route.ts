import { NextRequest, NextResponse } from 'next/server';
import { YNABService } from '@/lib/ynab-service';
import { generateDashboardSummary } from '@/lib/monthly-analysis';
import { getFirstDayOfMonth } from '@/lib/data-processing';
import { SecureErrorHandler } from '@/lib/errors';

/**
 * GET /api/analysis/monthly
 * Analyze monthly budget target alignment
 * 
 * Query parameters:
 * - budgetId: YNAB budget ID (optional, uses default if not provided)
 * - month: Month in YYYY-MM-DD format (optional, uses current month if not provided)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const budgetId = searchParams.get('budgetId');
    const month = searchParams.get('month');

    // Get budget (use default if not specified)
    const budget = budgetId 
      ? await YNABService.getBudget(budgetId)
      : await YNABService.getDefaultBudget();

    // Get month (use current if not specified)
    const analysisMonth = month || getFirstDayOfMonth();

    // Fetch month data
    const monthData = await YNABService.getMonth(budget.id, analysisMonth);

    // Generate comprehensive analysis
    const dashboardSummary = generateDashboardSummary(
      monthData,
      budget.id,
      budget.name
    );

    return NextResponse.json({
      success: true,
      data: dashboardSummary,
      metadata: {
        budgetId: budget.id,
        budgetName: budget.name,
        month: analysisMonth,
        generatedAt: new Date().toISOString(),
        cacheStats: YNABService.getCacheStats(),
      }
    });

  } catch (error) {
    console.error('Monthly analysis error:', error);
    
    const appError = SecureErrorHandler.handleAPIError(error, 'MONTHLY_ANALYSIS');
    
    return NextResponse.json({
      success: false,
      error: {
        type: appError.type,
        message: appError.userMessage,
        statusCode: appError.statusCode,
      }
    }, { status: appError.statusCode });
  }
}

/**
 * POST /api/analysis/monthly
 * Analyze monthly data with custom configuration
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { budgetId, month, config } = body;

    // Get budget
    const budget = budgetId 
      ? await YNABService.getBudget(budgetId)
      : await YNABService.getDefaultBudget();

    // Get month
    const analysisMonth = month || getFirstDayOfMonth();

    // Fetch month data
    const monthData = await YNABService.getMonth(budget.id, analysisMonth);

    // Generate analysis with custom config
    const dashboardSummary = generateDashboardSummary(
      monthData,
      budget.id,
      budget.name,
      config
    );

    return NextResponse.json({
      success: true,
      data: dashboardSummary,
      metadata: {
        budgetId: budget.id,
        budgetName: budget.name,
        month: analysisMonth,
        config,
        generatedAt: new Date().toISOString(),
      }
    });

  } catch (error) {
    console.error('Monthly analysis (POST) error:', error);
    
    const appError = SecureErrorHandler.handleAPIError(error, 'MONTHLY_ANALYSIS_POST');
    
    return NextResponse.json({
      success: false,
      error: {
        type: appError.type,
        message: appError.userMessage,
        statusCode: appError.statusCode,
      }
    }, { status: appError.statusCode });
  }
}
